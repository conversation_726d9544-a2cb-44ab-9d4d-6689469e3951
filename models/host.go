// Package models 定义主机资产管理相关的数据模型
package models

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// HostStatus 主机状态类型
type HostStatus string

const (
	HostStatusOnline   HostStatus = "online"   // 在线状态
	HostStatusOffline  HostStatus = "offline"  // 离线状态
	HostStatusMaintain HostStatus = "maintain" // 维护状态
)

// HostType 主机类型
type HostType string

const (
	HostTypePhysical HostType = "physical" // 物理机
	HostTypeVirtual  HostType = "virtual"  // 虚拟机
)

// DiskInfo 硬盘信息结构体
type DiskInfo struct {
	Size string `json:"size" bson:"size"` // 硬盘大小（如：500GB、1TB）
	Type string `json:"type" bson:"type"` // 硬盘类型（如：SSD、HDD、NVMe）
}

// Host 主机资产数据模型
type Host struct {
	ID              primitive.ObjectID `json:"id" bson:"_id,omitempty"`                // 主机唯一标识ID
	Hostname        string             `json:"hostname" bson:"hostname"`               // 主机名称
	IPAddress       string             `json:"ipAddress" bson:"ipAddress"`             // 主机IP地址，必须唯一
	CPU             int                `json:"cpu" bson:"cpu"`                         // CPU数量（核心数）
	Memory          int                `json:"memory" bson:"memory"`                   // 内存大小（单位：GB）
	Disks           []DiskInfo         `json:"disks" bson:"disks"`                     // 硬盘列表
	OperatingSystem string             `json:"operatingSystem" bson:"operatingSystem"` // 操作系统详细信息
	Status          HostStatus         `json:"status" bson:"status"`                   // 主机状态
	Type            HostType           `json:"type" bson:"type"`                       // 主机类型
	Location        string             `json:"location" bson:"location"`               // 主机位置
	Remarks         string             `json:"remarks" bson:"remarks"`                 // 备注信息
	CreateTime      string             `json:"createTime" bson:"createTime"`           // 主机创建时间
	UpdateTime      string             `json:"updateTime" bson:"updateTime"`           // 主机信息最后更新时间
}

// HostRequest 主机请求结构体（用于创建和更新）
type HostRequest struct {
	Hostname        string     `json:"hostname" binding:"required"`                             // 主机名称，必填
	IPAddress       string     `json:"ipAddress" binding:"required,ip"`                         // 主机IP地址，必填且必须是有效IP
	CPU             int        `json:"cpu" binding:"required,min=1"`                            // CPU数量，必填且至少为1
	Memory          int        `json:"memory" binding:"required,min=1"`                         // 内存大小，必填且至少为1GB
	OperatingSystem string     `json:"operatingSystem" binding:"required"`                      // 操作系统，必填
	Status          HostStatus `json:"status" binding:"required,oneof=online offline maintain"` // 主机状态，必填
	Type            HostType   `json:"type" binding:"required,oneof=virtual physical"`          // 主机类型，必填
	Location        string     `json:"location" binding:"required"`                             // 主机位置，必填
	Remarks         string     `json:"remarks,omitempty"`                                       // 备注信息，可选
}

// HostListRequest 主机列表查询请求结构体
type HostListRequest struct {
	Page            int        `form:"page,default=1"`                                                     // 页码
	PageSize        int        `form:"pageSize,default=10"`                                                // 每页数量
	Hostname        string     `form:"hostname,omitempty"`                                                 // 主机名称模糊查询
	IPAddress       string     `form:"ipAddress,omitempty"`                                                // IP地址模糊查询
	OperatingSystem string     `form:"operatingSystem,omitempty"`                                          // 操作系统模糊查询
	Status          HostStatus `form:"status,omitempty" binding:"omitempty,oneof=online offline maintain"` // 主机状态筛选
	Type            HostType   `form:"type,omitempty" binding:"omitempty,oneof=physical virtual"`          // 主机类型筛选
	Location        string     `form:"location,omitempty"`                                                 // 主机位置模糊查询
}

// HostListResponse 主机列表响应结构体
type HostListResponse struct {
	Hosts []Host `json:"hosts"` // 主机列表
	Total int64  `json:"total"` // 总数量
}
