// Package routers 路由管理中心，统一管理所有路由配置
package routers

import (
	"DunshanOps/middleware"

	"github.com/gin-gonic/gin"
)

// InitRoutes 初始化所有路由配置
// r: gin 引擎实例
func InitRoutes(r *gin.Engine) {
	// 设置 API 路由组
	api := r.Group("/api")
	{
		// 系统路由（健康检查）
		SetupSystemRoutes(api)

		// 认证相关路由（包含公开和需要认证的接口）
		SetupAuthRoutes(api)
	}

	// 需要JWT认证的路由组
	authRequired := api.Group("")
	authRequired.Use(middleware.JWTAuth())
	{
		// 用户管理路由
		SetupUserRoutes(authRequired)

		// 用户组管理路由
		SetupUserGroupRoutes(authRequired)

		// 主机资产管理路由
		SetupHostRoutes(authRequired)

	}

}
