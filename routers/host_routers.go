// Package routers 主机资产管理路由配置
package routers

import (
	"DunshanOps/handlers"

	"github.com/gin-gonic/gin"
)

// SetupHostRoutes 配置主机管理相关路由
// authGroup: 需要认证的路由组
func SetupHostRoutes(authGroup *gin.RouterGroup) {
	// 创建主机处理器实例
	hostHandler := handlers.NewHostHandler()

	// 主机管理路由组
	hostGroup := authGroup.Group("/assets/hosts")
	{
		// 获取主机列表 - GET /api/hosts/list
		hostGroup.GET("/list", hostHandler.GetHostList)

		// 创建主机 - POST /api/hosts/create
		hostGroup.POST("/create", hostHandler.CreateHost)

		// 获取主机信息 - GET /api/hosts/info
		hostGroup.GET("/info", hostHandler.GetHostByID)

		// 删除主机 - DELETE /api/hosts/delete
		hostGroup.DELETE("/delete", hostHandler.DeleteHost)

		// 更新主机 - PUT /api/hosts/update
		hostGroup.PUT("/update", hostHandler.UpdateHost)
	}
}
