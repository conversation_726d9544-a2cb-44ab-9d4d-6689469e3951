// Package services 主机资产管理服务模块
// 提供主机的增删改查操作以及IP地址唯一性验证功能
package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"DunshanOps/database"
	"DunshanOps/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	// HostCollectionName 主机资产集合名称
	HostCollectionName = "hosts_assets"
)

// HostService 主机服务结构体
type HostService struct {
	collection *mongo.Collection
}

// NewHostService 创建新的主机服务实例
func NewHostService() *HostService {
	service := &HostService{
		collection: database.GetCollection(HostCollectionName),
	}

	return service
}

// CreateHost 创建新主机
func (s *HostService) CreateHost(req *models.HostRequest) (*models.Host, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 检查IP地址是否已存在
	if exists, err := s.isIPAddressExists(ctx, req.IPAddress); err != nil {
		return nil, fmt.Errorf("检查IP地址唯一性时发生错误: %v", err)
	} else if exists {
		return nil, errors.New("IP地址已存在，请使用其他IP地址")
	}

	// 创建主机数据对象
	now := time.Now().Format(models.DateTimeLayout)
	host := &models.Host{
		ID:              primitive.NewObjectID(),
		Hostname:        req.Hostname,
		IPAddress:       req.IPAddress,
		CPU:             req.CPU,
		Memory:          req.Memory,
		Disks:           []models.DiskInfo{}, // 设置空的磁盘列表作为默认值
		OperatingSystem: req.OperatingSystem,
		Status:          req.Status,
		Type:            req.Type,
		Location:        req.Location,
		Remarks:         req.Remarks,
		CreateTime:      now,
		UpdateTime:      now,
	}

	// 插入数据库
	_, err := s.collection.InsertOne(ctx, host)
	if err != nil {
		return nil, fmt.Errorf("插入主机数据失败: %v", err)
	}

	return host, nil
}

// GetHostByID 根据ID获取主机信息
func (s *HostService) GetHostByID(hostID string) (*models.Host, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 转换字符串ID为ObjectID
	objectID, err := primitive.ObjectIDFromHex(hostID)
	if err != nil {
		return nil, errors.New("无效的主机ID格式")
	}

	var host models.Host
	err = s.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&host)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("主机不存在")
		}
		return nil, fmt.Errorf("查询主机数据失败: %v", err)
	}

	return &host, nil
}

// UpdateHost 更新主机信息
func (s *HostService) UpdateHost(hostID string, req *models.HostUpdateRequest) (*models.Host, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 转换字符串ID为ObjectID
	objectID, err := primitive.ObjectIDFromHex(hostID)
	if err != nil {
		return nil, errors.New("无效的主机ID格式")
	}

	// 检查主机是否存在
	existingHost, err := s.GetHostByID(hostID)
	if err != nil {
		return nil, err
	}

	// 检查新IP地址是否已被其他主机使用（IP地址现在是必填的）
	if req.IPAddress != existingHost.IPAddress {
		if exists, err := s.isIPAddressExistsExcludingID(ctx, req.IPAddress, objectID); err != nil {
			return nil, fmt.Errorf("检查IP地址唯一性时发生错误: %v", err)
		} else if exists {
			return nil, errors.New("IP地址已存在，请使用其他IP地址")
		}
	}

	// 构建更新字段 - 现在所有核心字段都是必填的，直接更新
	update := bson.M{
		"$set": bson.M{
			"hostname":        req.Hostname,
			"ipAddress":       req.IPAddress,
			"cpu":             req.CPU,
			"memory":          req.Memory,
			"operatingSystem": req.OperatingSystem,
			"status":          req.Status,
			"type":            req.Type,
			"location":        req.Location,
			"remarks":         req.Remarks,
			"updateTime":      time.Now().Format(models.DateTimeLayout),
		},
	}

	// 执行更新操作
	_, err = s.collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return nil, fmt.Errorf("更新主机数据失败: %v", err)
	}

	// 返回更新后的主机信息
	return s.GetHostByID(hostID)
}

// DeleteHost 删除主机
func (s *HostService) DeleteHost(hostID string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 转换字符串ID为ObjectID
	objectID, err := primitive.ObjectIDFromHex(hostID)
	if err != nil {
		return errors.New("无效的主机ID格式")
	}

	// 检查主机是否存在
	_, err = s.GetHostByID(hostID)
	if err != nil {
		return err
	}

	// 执行删除操作
	result, err := s.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("删除主机数据失败: %v", err)
	}

	if result.DeletedCount == 0 {
		return errors.New("删除主机失败，主机可能已被删除")
	}

	return nil
}

// GetHostList 获取主机列表（支持分页和筛选）
func (s *HostService) GetHostList(req *models.HostListRequest) (*models.HostListResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}

	// 添加模糊查询条件
	if req.Hostname != "" {
		filter["hostname"] = bson.M{"$regex": req.Hostname, "$options": "i"}
	}
	if req.IPAddress != "" {
		filter["ipAddress"] = bson.M{"$regex": req.IPAddress, "$options": "i"}
	}
	if req.OperatingSystem != "" {
		filter["operatingSystem"] = bson.M{"$regex": req.OperatingSystem, "$options": "i"}
	}
	if req.Location != "" {
		filter["location"] = bson.M{"$regex": req.Location, "$options": "i"}
	}

	// 添加精确匹配条件
	if req.Status != "" {
		filter["status"] = req.Status
	}
	if req.Type != "" {
		filter["type"] = req.Type
	}

	// 计算总数量
	totalCount, err := s.collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("统计主机总数失败: %v", err)
	}

	// 计算分页参数
	skip := (req.Page - 1) * req.PageSize
	findOptions := options.Find()
	findOptions.SetSkip(int64(skip))
	findOptions.SetLimit(int64(req.PageSize))
	findOptions.SetSort(bson.D{{Key: "createTime", Value: -1}}) // 按创建时间倒序排列

	// 查询主机列表
	cursor, err := s.collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("查询主机列表失败: %v", err)
	}
	defer cursor.Close(ctx)

	var hosts []models.Host
	if err = cursor.All(ctx, &hosts); err != nil {
		return nil, fmt.Errorf("解析主机列表数据失败: %v", err)
	}

	// 如果没有数据，返回空列表而不是nil
	if hosts == nil {
		hosts = []models.Host{}
	}

	return &models.HostListResponse{
		Hosts: hosts,
		Total: totalCount,
	}, nil
}

// // GetHostByIPAddress 根据IP地址获取主机信息
// func (s *HostService) GetHostByIPAddress(ipAddress string) (*models.Host, error) {
// 	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
// 	defer cancel()

// 	var host models.Host
// 	err := s.collection.FindOne(ctx, bson.M{"ipAddress": ipAddress}).Decode(&host)
// 	if err != nil {
// 		if err == mongo.ErrNoDocuments {
// 			return nil, nil // IP地址不存在，返回nil但不报错
// 		}
// 		return nil, fmt.Errorf("查询主机数据失败: %v", err)
// 	}

// 	return &host, nil
// }

// isIPAddressExists 检查IP地址是否已存在
func (s *HostService) isIPAddressExists(ctx context.Context, ipAddress string) (bool, error) {
	count, err := s.collection.CountDocuments(ctx, bson.M{"ipAddress": ipAddress})
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// isIPAddressExistsExcludingID 检查IP地址是否已存在（排除指定ID的主机）
func (s *HostService) isIPAddressExistsExcludingID(ctx context.Context, ipAddress string, excludeID primitive.ObjectID) (bool, error) {
	filter := bson.M{
		"ipAddress": ipAddress,
		"_id":       bson.M{"$ne": excludeID},
	}
	count, err := s.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// // ensureIndexes 确保数据库索引存在，如果不存在则创建
// func (s *HostService) ensureIndexes() error {
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()

// 	// 检查是否已存在IP地址索引
// 	cursor, err := s.collection.Indexes().List(ctx)
// 	if err != nil {
// 		return fmt.Errorf("列出索引失败: %v", err)
// 	}
// 	defer cursor.Close(ctx)

// 	// 检查是否已存在ipAddress字段的唯一索引
// 	hasIPIndex := false
// 	for cursor.Next(ctx) {
// 		var index bson.M
// 		if err := cursor.Decode(&index); err != nil {
// 			continue
// 		}

// 		// 检查索引的key字段
// 		if key, ok := index["key"].(bson.M); ok {
// 			if _, hasIP := key["ipAddress"]; hasIP {
// 				hasIPIndex = true
// 				break
// 			}
// 		}
// 	}

// 	// 如果没有IP地址索引，则创建
// 	if !hasIPIndex {
// 		indexModel := mongo.IndexModel{
// 			Keys:    bson.D{{Key: "ipAddress", Value: 1}},
// 			Options: options.Index().SetUnique(true).SetName("idx_ip_address_unique"),
// 		}

// 		_, err := s.collection.Indexes().CreateOne(ctx, indexModel)
// 		if err != nil {
// 			return fmt.Errorf("创建IP地址唯一索引失败: %v", err)
// 		}
// 	}

// 	return nil
// }
