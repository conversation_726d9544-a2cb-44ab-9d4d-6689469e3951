// Package handlers 主机资产管理相关的 HTTP 处理器
package handlers

import (
	"net/http"

	"DunshanOps/logger"
	"DunshanOps/models"
	"DunshanOps/pkg/response"
	"DunshanOps/services"

	"github.com/gin-gonic/gin"
)

// HostHandler 主机处理器结构体
type HostHandler struct {
	hostService *services.HostService
}

// NewHostHandler 创建新的主机处理器实例
func NewHostHandler() *HostHandler {
	return &HostHandler{
		hostService: services.NewHostService(),
	}
}

// CreateHost 创建主机
func (h *HostHandler) CreateHost(c *gin.Context) {
	var req models.HostCreateRequest

	// 解析请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "create_host").
			Err(err).
			Msg("创建主机请求参数解析失败")
		c.J<PERSON>(http.StatusOK, response.ErrorResponse(400, "创建主机失败：请求参数格式错误"))
		return
	}

	// 调用服务创建主机
	host, err := h.hostService.CreateHost(&req)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "create_host").
			Str("hostname", req.Hostname).
			Str("ipAddress", req.IPAddress).
			Err(err).
			Msg("创建主机失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "创建主机失败："+err.Error()))
		return
	}

	logger.APPLogger.Info().
		Str("operation", "create_host").
		Str("hostID", host.ID.Hex()).
		Str("hostname", host.Hostname).
		Str("ipAddress", host.IPAddress).
		Msg("主机创建成功")

	c.JSON(http.StatusOK, response.SuccessResponse(host))
}

// GetHostByID 根据ID获取单个主机信息
func (h *HostHandler) GetHostByID(c *gin.Context) {
	hostID := c.Query("id")
	if hostID == "" {
		logger.APPLogger.Warn().
			Str("operation", "get_host").
			Msg("获取主机信息失败：缺少主机ID参数")
		c.JSON(http.StatusOK, response.ErrorResponse(400, "获取主机信息失败：缺少主机ID参数"))
		return
	}

	// 调用服务获取主机信息
	host, err := h.hostService.GetHostByID(hostID)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "get_host").
			Str("hostID", hostID).
			Err(err).
			Msg("获取主机信息失败")
		c.JSON(http.StatusOK, response.ErrorResponse(202, "获取主机信息失败："+err.Error()))
		return
	}

	c.JSON(http.StatusOK, response.SuccessResponse(host))
}

// UpdateHost 更新主机信息
func (h *HostHandler) UpdateHost(c *gin.Context) {
	hostID := c.Query("id")
	if hostID == "" {
		logger.APPLogger.Warn().
			Str("operation", "update_host").
			Msg("更新主机信息失败：缺少主机ID参数")
		c.JSON(http.StatusOK, response.ErrorResponse(400, "更新主机信息失败：缺少主机ID参数"))
		return
	}

	var req models.HostUpdateRequest

	// 解析请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "update_host").
			Str("hostID", hostID).
			Err(err).
			Msg("更新主机信息请求参数解析失败")
		c.JSON(http.StatusOK, response.ErrorResponse(400, "更新主机信息失败：请求参数格式错误"))
		return
	}

	// 调用服务更新主机信息
	host, err := h.hostService.UpdateHost(hostID, &req)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "update_host").
			Str("hostID", hostID).
			Err(err).
			Msg("更新主机信息失败")
		c.JSON(http.StatusOK, response.ErrorResponse(203, "更新主机信息失败："+err.Error()))
		return
	}

	logger.APPLogger.Info().
		Str("operation", "update_host").
		Str("hostID", hostID).
		Str("hostname", host.Hostname).
		Msg("主机信息更新成功")

	c.JSON(http.StatusOK, response.SuccessResponse(host))
}

// DeleteHost 删除主机
func (h *HostHandler) DeleteHost(c *gin.Context) {
	hostID := c.Query("id")
	if hostID == "" {
		logger.APPLogger.Warn().
			Str("operation", "delete_host").
			Msg("删除主机失败：缺少主机ID参数")
		c.JSON(http.StatusOK, response.ErrorResponse(400, "删除主机失败：缺少主机ID参数"))
		return
	}

	// 获取主机信息用于日志记录
	host, err := h.hostService.GetHostByID(hostID)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "delete_host").
			Str("hostID", hostID).
			Err(err).
			Msg("删除主机失败：获取主机信息失败")
		c.JSON(http.StatusOK, response.ErrorResponse(204, "删除主机失败："+err.Error()))
		return
	}

	// 调用服务删除主机
	err = h.hostService.DeleteHost(hostID)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "delete_host").
			Str("hostID", hostID).
			Err(err).
			Msg("删除主机失败")
		c.JSON(http.StatusOK, response.ErrorResponse(204, "删除主机失败："+err.Error()))
		return
	}

	logger.APPLogger.Info().
		Str("operation", "delete_host").
		Str("hostID", hostID).
		Str("hostname", host.Hostname).
		Str("ipAddress", host.IPAddress).
		Msg("主机删除成功")

	c.JSON(http.StatusOK, response.SuccessResponse(nil))
}

// GetHostList 获取主机列表
func (h *HostHandler) GetHostList(c *gin.Context) {
	var req models.HostListRequest

	// 解析查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "get_host_list").
			Err(err).
			Msg("获取主机列表请求参数解析失败")
		c.JSON(http.StatusOK, response.ErrorResponse(400, "获取主机列表失败：请求参数格式错误"))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	// 限制每页最大数量，防止查询过多数据
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 调用服务获取主机列表
	result, err := h.hostService.GetHostList(&req)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "get_host_list").
			Int("page", req.Page).
			Int("pageSize", req.PageSize).
			Err(err).
			Msg("获取主机列表失败")
		c.JSON(http.StatusOK, response.ErrorResponse(205, "获取主机列表失败："+err.Error()))
		return
	}

	logger.APPLogger.Info().
		Str("operation", "get_host_list").
		Int("page", req.Page).
		Int("pageSize", req.PageSize).
		Int64("total", result.Total).
		Int("count", len(result.Hosts)).
		Msg("获取主机列表成功")

	c.JSON(http.StatusOK, response.SuccessResponse(result))
}